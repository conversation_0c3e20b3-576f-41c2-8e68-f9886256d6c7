<template>
    <div id="app">
        <router-view />
    </div>
</template>
<style lang="scss">
#app {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB',
        'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    color: #1e293b;
    height: 100vh;
    background: linear-gradient(180deg, #f8fafc 0%, #ffffff 100%);
    overflow-x: hidden;
}

// 全局隐藏滚动条
::-webkit-scrollbar {
    width: 0px;
    height: 0px;
    background: transparent;
}

::-webkit-scrollbar-track {
    background: transparent;
}

::-webkit-scrollbar-thumb {
    background: transparent;
}

// 兼容Firefox
html {
    scrollbar-width: none;
}

// 兼容IE和Edge
body {
    -ms-overflow-style: none;
}

// 路由过渡动画
.router-fade-enter-active,
.router-fade-leave-active {
    transition: opacity 0.3s ease;
}

.router-fade-enter,
.router-fade-leave-to {
    opacity: 0;
}

// 通用页面容器样式
.page-container {
    min-height: 100vh;
    background: linear-gradient(180deg, #f8fafc 0%, #ffffff 100%);
    padding-bottom: 60px; // 为底部导航栏留出空间
}

// 通用内容区域样式
.content-wrapper {
    padding: 20px 16px;
    max-width: 100%;
    margin: 0 auto;
}

// 通用卡片样式
.common-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 16px;
    box-shadow: 0 4px 24px rgba(37, 99, 235, 0.08);
    border: 1px solid rgba(37, 99, 235, 0.05);
    transition: all 0.3s ease;

    &:active {
        transform: scale(0.98);
        box-shadow: 0 8px 32px rgba(37, 99, 235, 0.15);
    }
}

// 通用按钮样式
.primary-button {
    background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%);
    border: none;
    border-radius: 12px;
    color: white;
    font-weight: 600;
    box-shadow: 0 4px 20px rgba(37, 99, 235, 0.2);
    transition: all 0.3s ease;

    &:active {
        transform: scale(0.98);
        box-shadow: 0 8px 32px rgba(37, 99, 235, 0.3);
    }
}

// 通用文字样式
.text-primary {
    color: #1e293b;
}

.text-secondary {
    color: #64748b;
}

.text-blue {
    color: #2563eb;
}

.font-bold {
    font-weight: 700;
}

.font-semibold {
    font-weight: 600;
}

.font-medium {
    font-weight: 500;
}
</style>
