// 导入path模块
const path = require('path')

module.exports = {
    publicPath: '/',
    assetsDir: 'static',
    productionSourceMap: false,
    devServer: {
        port: 8080,
        open: false,

        proxy: {
            '/api': {
                target: 'http://192.168.0.173:8033',
                changeOrigin: true,
                secure: false,
                logLevel: 'debug',
                pathRewrite: {
                    '^/api': '/api'
                }
            }
        }
    },
    configureWebpack: {
        resolve: {
            extensions: ['.js', '.vue', '.json'],
            alias: {
                '@': path.resolve(__dirname, 'src')
            }
        }
    }
}
