<template>
    <div class="study-page">
        <van-nav-bar title="我的学习" fixed placeholder />

        <div class="content">
            <!-- 学习统计 -->
            <div class="study-stats">
                <van-grid :column-num="3" :border="false">
                    <van-grid-item>
                        <div class="stat-item">
                            <div class="stat-number">{{ studyStats.totalCourses }}</div>
                            <div class="stat-label">已学课程</div>
                        </div>
                    </van-grid-item>
                    <van-grid-item>
                        <div class="stat-item">
                            <div class="stat-number">{{ studyStats.totalHours }}</div>
                            <div class="stat-label">学习时长(小时)</div>
                        </div>
                    </van-grid-item>
                    <van-grid-item>
                        <div class="stat-item">
                            <div class="stat-number">{{ studyStats.certificates }}</div>
                            <div class="stat-label">获得证书</div>
                        </div>
                    </van-grid-item>
                </van-grid>
            </div>

            <!-- 学习进度 -->
            <div class="progress-section">
                <van-cell-group>
                    <van-cell
                        title="本周学习目标"
                        :value="weeklyGoal.current + '/' + weeklyGoal.target + '小时'"
                    >
                        <template #right-icon>
                            <van-progress
                                :percentage="weeklyGoal.percentage"
                                stroke-width="6"
                                color="#1989fa"
                            />
                        </template>
                    </van-cell>
                </van-cell-group>
            </div>

            <!-- 最近学习 -->
            <div class="recent-study">
                <van-divider content-position="left">最近学习</van-divider>
                <van-list>
                    <div
                        v-for="item in recentStudy"
                        :key="item.id"
                        class="study-item"
                        @click="continueLearning(item)"
                    >
                        <van-card
                            :title="item.courseTitle"
                            :desc="'上次学习: ' + item.lastStudyTime"
                            :thumb="item.cover"
                        >
                            <template #num>
                                <van-progress
                                    :percentage="item.progress"
                                    stroke-width="4"
                                    color="#1989fa"
                                />
                                <span class="progress-text">{{ item.progress }}%</span>
                            </template>
                            <template #footer>
                                <van-button size="mini" type="primary">继续学习</van-button>
                            </template>
                        </van-card>
                    </div>
                </van-list>
            </div>

            <!-- 学习计划 -->
            <div class="study-plan">
                <van-divider content-position="left">学习计划</van-divider>
                <van-cell-group>
                    <van-cell
                        v-for="plan in studyPlans"
                        :key="plan.id"
                        :title="plan.title"
                        :label="plan.description"
                        :value="plan.deadline"
                        is-link
                        @click="viewPlan(plan)"
                    />
                </van-cell-group>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'StudyPage',
    data() {
        return {
            studyStats: {
                totalCourses: 12,
                totalHours: 48,
                certificates: 3
            },
            weeklyGoal: {
                current: 8,
                target: 10,
                percentage: 80
            },
            recentStudy: [
                {
                    id: 1,
                    courseTitle: '人体解剖学基础',
                    lastStudyTime: '2小时前',
                    progress: 75,
                    cover: 'https://img.yzcdn.cn/vant/cat.jpeg'
                },
                {
                    id: 2,
                    courseTitle: '内科学精讲',
                    lastStudyTime: '昨天',
                    progress: 45,
                    cover: 'https://img.yzcdn.cn/vant/cat.jpeg'
                }
            ],
            studyPlans: [
                {
                    id: 1,
                    title: '基础医学认证',
                    description: '完成基础医学相关课程学习',
                    deadline: '2024-03-15'
                },
                {
                    id: 2,
                    title: '临床技能提升',
                    description: '掌握常见临床操作技能',
                    deadline: '2024-04-20'
                }
            ]
        }
    },
    methods: {
        continueLearning(item) {
            console.log('继续学习:', item.courseTitle)
            // 跳转到课程学习页面
            this.$router.push(`/course-study/${item.id}`)
        },
        viewPlan(plan) {
            console.log('查看计划:', plan.title)
            // 跳转到学习计划详情
            this.$router.push(`/study-plan/${plan.id}`)
        }
    }
}
</script>

<style lang="scss" scoped>
// 蓝白主题色彩变量
$primary-blue: #2563eb;
$light-blue: #3b82f6;
$lighter-blue: #60a5fa;
$lightest-blue: #dbeafe;
$background-blue: #f8fafc;
$white: #ffffff;
$text-primary: #1e293b;
$text-secondary: #64748b;
$text-light: #94a3b8;

.study-page {
    background: linear-gradient(180deg, $background-blue 0%, #ffffff 100%);
    min-height: 100vh;
    overflow-x: hidden;

    // 隐藏滚动条
    scrollbar-width: none;
    -ms-overflow-style: none;

    &::-webkit-scrollbar {
        display: none;
    }

    .content {
        padding: 20px 16px;

        .study-stats {
            background: linear-gradient(135deg, $white 0%, #f1f5f9 100%);
            border-radius: 16px;
            margin-bottom: 24px;
            padding: 24px 0;
            box-shadow: 0 4px 24px rgba(37, 99, 235, 0.08);
            border: 1px solid rgba(37, 99, 235, 0.05);

            .stat-item {
                text-align: center;

                .stat-number {
                    font-size: 28px;
                    font-weight: 700;
                    color: $primary-blue;
                    margin-bottom: 6px;
                }

                .stat-label {
                    font-size: 13px;
                    color: $text-secondary;
                    font-weight: 600;
                }
            }
        }

        .progress-section {
            margin-bottom: 24px;

            ::v-deep .van-cell {
                background: linear-gradient(135deg, $white 0%, #f8fafc 100%);
                border-radius: 16px;
                box-shadow: 0 4px 20px rgba(37, 99, 235, 0.08);
                border: 1px solid rgba(37, 99, 235, 0.05);
                padding: 20px 16px;

                .van-cell__title {
                    color: $text-primary;
                    font-weight: 600;
                    font-size: 16px;
                }

                .van-cell__value {
                    color: $text-secondary;
                    font-weight: 600;
                }

                .van-progress {
                    margin-top: 8px;

                    .van-progress__portion {
                        background: linear-gradient(135deg, $primary-blue 0%, $light-blue 100%);
                    }
                }
            }
        }

        .recent-study,
        .study-plan {
            margin-bottom: 24px;

            .study-item {
                margin-bottom: 16px;

                ::v-deep .van-card {
                    background: linear-gradient(135deg, $white 0%, #f8fafc 100%);
                    border-radius: 16px;
                    box-shadow: 0 4px 24px rgba(37, 99, 235, 0.08);
                    border: 1px solid rgba(37, 99, 235, 0.05);
                    transition: all 0.3s ease;

                    &:active {
                        transform: scale(0.98);
                        box-shadow: 0 8px 32px rgba(37, 99, 235, 0.15);
                    }

                    .van-card__header {
                        padding: 16px;
                    }

                    .van-card__title {
                        color: $text-primary;
                        font-weight: 700;
                        font-size: 16px;
                    }

                    .van-card__desc {
                        color: $text-secondary;
                        font-weight: 500;
                    }

                    .van-card__footer {
                        padding: 16px;

                        .van-button {
                            background: linear-gradient(135deg, $primary-blue 0%, $light-blue 100%);
                            border: none;
                            border-radius: 12px;
                            font-weight: 600;
                            box-shadow: 0 4px 16px rgba(37, 99, 235, 0.2);
                        }
                    }
                }

                .progress-text {
                    font-size: 12px;
                    color: $text-secondary;
                    margin-left: 8px;
                    font-weight: 500;
                }
            }

            ::v-deep .van-cell-group {
                background: linear-gradient(135deg, $white 0%, #f8fafc 100%);
                border-radius: 16px;
                box-shadow: 0 4px 20px rgba(37, 99, 235, 0.08);
                border: 1px solid rgba(37, 99, 235, 0.05);
                overflow: hidden;

                .van-cell {
                    background: transparent;
                    padding: 18px 20px;

                    &:not(:last-child) {
                        border-bottom: 1px solid rgba(37, 99, 235, 0.06);
                    }

                    .van-cell__title {
                        color: $text-primary;
                        font-weight: 600;
                    }

                    .van-cell__label {
                        color: $text-secondary;
                        font-weight: 500;
                    }

                    .van-cell__value {
                        color: $primary-blue;
                        font-weight: 600;
                    }
                }
            }

            ::v-deep .van-divider {
                color: $text-primary;
                font-weight: 700;
                font-size: 18px;
                margin: 24px 0 16px;

                &::before,
                &::after {
                    border-color: rgba(37, 99, 235, 0.1);
                }
            }
        }
    }
}
</style>
