import { axiosInstance } from '@/boot/main/axios'

// 获取课程列表
export function getCourseList(params) {
    return axiosInstance({
        url: '/api/otms/course',
        method: 'get',
        params
    })
}

// 获取课程详情
export function getCourseDetail(id) {
    return axiosInstance({
        url: `/api/otms/course/${id}`,
        method: 'get'
    })
}

// 报名课程
export function enrollCourse(data) {
    return axiosInstance({
        url: '/api/otms/course/student',
        method: 'post',
        data
    })
}
// 查询报名课程
export function getEnrollCourse(params) {
    return axiosInstance({
        url: '/api/otms/course/student',
        method: 'get',
        params
    })
}

// 获取课程资源
export function getCourseResources(data) {
    return axiosInstance({
        url: '/api/course/resources',
        method: 'post',
        data: {
            AppID: process.env.VUE_APP_ID || 'medical-training-system',
            courseId: data.courseId,
            moduleId: data.moduleId || ''
        }
    })
}

// 获取课程分类
export function getCourseCategories() {
    return axiosInstance({
        url: '/api/otms/course/category',
        method: 'get'
    })
}

// 获取课程进度
export function getCourseProgress(params) {
    return axiosInstance({
        url: '/api/otms/learning/progress',
        method: 'get',
        params
    })
}
